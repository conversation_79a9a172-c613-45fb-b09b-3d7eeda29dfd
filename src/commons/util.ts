import { Debounced } from "../types/commonTypes"

export function timeframe2timeUnit(timeframe: string): number {
    switch (timeframe) {
        case '1m':
            return 60 * 1000
        case '15m':
            return 15 * 60 * 1000
        case '3m':
            return 3 * 60 * 1000
        case '5m':
            return 5 * 60 * 1000
        case '30m':
            return 30 * 60 * 1000
        case '1h':
            return 60 * 60 * 1000
        case '4h':
            return 4 * 60 * 60 * 1000
        case '1d':
            return 24 * 60 * 60 * 1000
        default:
            throw new Error('invalid timeframe')
    }
}


export function findClosestDivisible(
    number: number,
    divisor: number,
    snapDirection: 'lower' | 'higher' | 'closest' = 'closest'
): number {
    const lower = Math.floor(number / divisor) * divisor;
    const higher = Math.ceil(number / divisor) * divisor;

    switch (snapDirection) {
        case 'lower':
            return lower;
        case 'higher':
            return higher;
        case 'closest':
        default:
            return Math.abs(number - lower) < Math.abs(higher - number) ? lower : higher;
    }
}

export function disableDebug() {
    console.debug = function () { };
}

export function getFormatNumStr(num: number) {

    let text = "";
    if (Math.abs(num) < 0.4) {
        text = num.toFixed(5);
    } else if (Math.abs(num) < 1) {
        text = num.toFixed(4);
    }
    else {
        text = num.toFixed(3);
    }
    return text
}

export function navigateTo(url: string) {
    window.history.pushState({}, '', url);
    // Dispatch a popstate event to trigger URL parameter handling
    window.dispatchEvent(new PopStateEvent('popstate'));
}

export function debounce<T extends (...args: any[]) => any>(
    func: T,
    wait: number
): Debounced<T> {
    let timeout: ReturnType<typeof setTimeout> | null;

    return ((...args: Parameters<T>) => {
        return new Promise((resolve) => {
            if (timeout !== null) {
                clearTimeout(timeout);
            }

            timeout = setTimeout(() => {
                timeout = null;
                const result = func(...args);
                resolve(result);
            }, wait);
        });
    }) as Debounced<T>;
}

export function isNil(value: unknown): value is null | undefined {
    return value === null || value === undefined;
}

// Checks if value is a number (including NaN and Infinity)
export function isNumber(value: unknown): value is number {
    return typeof value === 'number';
}

// Get adaptive time format based on visible time span
export function getAdaptiveTimeFormat(timeSpanMs: number): string {
    const timeSpanMinutes = timeSpanMs / (60 * 1000);
    const timeSpanHours = timeSpanMs / (60 * 60 * 1000);
    const timeSpanDays = timeSpanMs / (24 * 60 * 60 * 1000);

    // For very short time spans (less than 1 hour visible), show seconds
    if (timeSpanMinutes < 60) {
        return 'HH:mm:ss';
    }
    // For short time spans (less than 1 day visible), show minutes
    else if (timeSpanHours < 24) {
        return 'MM/dd HH:mm';
    }
    // For medium time spans (less than 30 days visible), show hours
    else if (timeSpanDays < 30) {
        return 'MM/dd HH:mm';
    }
    // For long time spans, show just date
    else {
        return 'MM/dd';
    }
}

